{"metadata": {"generated_at": "2025-08-04", "total_original_files": 49, "total_new_files": 57, "net_change": 8, "description": "Notion-to-WordPress 项目架构重构文件映射关系"}, "statistics": {"by_status": {"保持不变": 4, "重命名迁移": 8, "目录重组": 2, "重命名+重组": 15, "跨层迁移": 9, "功能重构": 1, "已删除": 2, "新增": 6, "架构重构": 2}, "by_risk_level": {"低风险": 14, "中风险": 17, "高风险": 10, "最高风险": 10}}, "mappings": [{"id": "fw_001", "original_path": "framework/Main.php", "new_path": "framework/Main.php", "status": "保持不变", "migration_type": "unchanged", "risk_level": "低风险", "layer": "Framework", "description": "插件核心类，负责初始化和依赖管理", "notes": "无变化"}, {"id": "fw_002", "original_path": "framework/Loader.php", "new_path": "framework/Loader.php", "status": "保持不变", "migration_type": "unchanged", "risk_level": "低风险", "layer": "Framework", "description": "插件加载器，管理钩子注册", "notes": "无变化"}, {"id": "fw_003", "original_path": "framework/i18n.php", "new_path": "framework/I18n.php", "status": "重命名迁移", "migration_type": "rename", "risk_level": "低风险", "layer": "Framework", "description": "国际化处理类", "notes": "文件名大小写规范化"}, {"id": "core_001", "original_path": "core/Logger.php", "new_path": "core/Foundation/Logger.php", "status": "目录重组", "migration_type": "directory_move", "risk_level": "中风险", "layer": "Core", "sublayer": "Foundation", "description": "日志记录和调试管理", "notes": "迁移到Foundation子目录"}, {"id": "core_002", "original_path": "core/Security.php", "new_path": "core/Foundation/Security.php", "status": "目录重组", "migration_type": "directory_move", "risk_level": "中风险", "layer": "Core", "sublayer": "Foundation", "description": "安全验证和权限控制", "notes": "迁移到Foundation子目录"}, {"id": "core_003", "original_path": "core/Error_Handler.php", "new_path": "core/Foundation/ErrorHandler.php", "status": "重命名+重组", "migration_type": "rename_and_move", "risk_level": "中风险", "layer": "Core", "sublayer": "Foundation", "description": "错误处理和异常管理", "notes": "重命名并迁移到Foundation子目录"}, {"id": "core_004", "original_path": "core/Dependency_Container.php", "new_path": "core/Foundation/Container.php", "status": "重命名+重组", "migration_type": "rename_and_move", "risk_level": "中风险", "layer": "Core", "sublayer": "Foundation", "description": "依赖注入容器", "notes": "重命名并迁移到Foundation子目录"}, {"id": "core_005", "original_path": "services/API.php", "new_path": "core/Foundation/ApiErrorHandler.php", "status": "功能重构", "migration_type": "functional_refactor", "risk_level": "高风险", "layer": "Core", "sublayer": "Foundation", "description": "API错误处理专用", "notes": "从services层重构到core层，功能专门化"}, {"id": "core_006", "original_path": "core/HTTP_Client.php", "new_path": "core/Network/HttpClient.php", "status": "重命名+重组", "migration_type": "rename_and_move", "risk_level": "中风险", "layer": "Core", "sublayer": "Network", "description": "HTTP客户端封装", "notes": "重命名并迁移到Network子目录"}, {"id": "core_007", "original_path": "utils/Stream_Processor.php", "new_path": "core/Network/StreamProcessor.php", "status": "跨层迁移", "migration_type": "cross_layer_migration", "risk_level": "高风险", "layer": "Core", "sublayer": "Network", "description": "流数据处理", "notes": "从utils层迁移到core/Network层"}, {"id": "core_008", "original_path": "core/Performance_Monitor.php", "new_path": "core/Performance/PerformanceMonitor.php", "status": "重命名+重组", "migration_type": "rename_and_move", "risk_level": "中风险", "layer": "Core", "sublayer": "Performance", "description": "性能监控和统计", "notes": "重命名并迁移到Performance子目录"}, {"id": "core_009", "original_path": "core/Progress_Tracker.php", "new_path": "core/Performance/ProgressTracker.php", "status": "重命名+重组", "migration_type": "rename_and_move", "risk_level": "中风险", "layer": "Core", "sublayer": "Performance", "description": "进度跟踪管理", "notes": "重命名并迁移到Performance子目录"}, {"id": "core_010", "original_path": "core/Algorithm_Optimizer.php", "new_path": "core/Performance/AlgorithmOptimizer.php", "status": "重命名+重组", "migration_type": "rename_and_move", "risk_level": "中风险", "layer": "Core", "sublayer": "Performance", "description": "算法优化器", "notes": "重命名并迁移到Performance子目录"}, {"id": "core_011", "original_path": "-", "new_path": "core/Performance/BatchOptimizer.php", "status": "新增", "migration_type": "new_file", "risk_level": "中风险", "layer": "Core", "sublayer": "Performance", "description": "批处理优化器", "notes": "新增文件，需要确认功能来源"}, {"id": "core_012", "original_path": "core/Task_Executor.php", "new_path": "core/Task/TaskExecutor.php", "status": "重命名+重组", "migration_type": "rename_and_move", "risk_level": "中风险", "layer": "Core", "sublayer": "Task", "description": "任务执行器", "notes": "重命名并迁移到Task子目录"}, {"id": "core_013", "original_path": "core/Async_Task_Scheduler.php", "new_path": "core/Task/AsyncTaskScheduler.php", "status": "重命名+重组", "migration_type": "rename_and_move", "risk_level": "中风险", "layer": "Core", "sublayer": "Task", "description": "异步任务调度器", "notes": "重命名并迁移到Task子目录"}, {"id": "core_014", "original_path": "core/Modern_Async_Engine.php", "new_path": "core/Task/ModernAsyncEngine.php", "status": "重命名+重组", "migration_type": "rename_and_move", "risk_level": "中风险", "layer": "Core", "sublayer": "Task", "description": "现代异步引擎", "notes": "重命名并迁移到Task子目录"}, {"id": "infra_001", "original_path": "utils/Session_Cache.php", "new_path": "infrastructure/Cache/SessionCache.php", "status": "跨层迁移", "migration_type": "cross_layer_migration", "risk_level": "高风险", "layer": "Infrastructure", "sublayer": "<PERSON><PERSON>", "description": "会话缓存管理", "notes": "从utils层迁移到infrastructure/Cache层"}, {"id": "infra_002", "original_path": "utils/Smart_Cache.php", "new_path": "infrastructure/Cache/CacheManager.php", "status": "重命名+重组", "migration_type": "rename_and_move", "risk_level": "中风险", "layer": "Infrastructure", "sublayer": "<PERSON><PERSON>", "description": "智能缓存管理器", "notes": "重命名并迁移到infrastructure/Cache层"}, {"id": "infra_003", "original_path": "core/Dynamic_Concurrency_Manager.php", "new_path": "infrastructure/Concurrency/ConcurrencyManager.php", "status": "跨层迁移", "migration_type": "cross_layer_migration", "risk_level": "高风险", "layer": "Infrastructure", "sublayer": "Concurrency", "description": "并发管理器", "notes": "从core层迁移到infrastructure/Concurrency层"}, {"id": "infra_004", "original_path": "core/Task_Queue.php", "new_path": "infrastructure/Concurrency/TaskQueue.php", "status": "跨层迁移", "migration_type": "cross_layer_migration", "risk_level": "高风险", "layer": "Infrastructure", "sublayer": "Concurrency", "description": "任务队列管理", "notes": "从core层迁移到infrastructure/Concurrency层"}, {"id": "infra_005", "original_path": "utils/Unified_Concurrency_Manager.php", "new_path": "-", "status": "已删除", "migration_type": "deleted", "risk_level": "最高风险", "layer": "Infrastructure", "sublayer": "Concurrency", "description": "统一并发管理器", "notes": "功能已合并到ConcurrencyManager，需要验证功能完整性", "merge_target": "infrastructure/Concurrency/ConcurrencyManager.php"}, {"id": "infra_006", "original_path": "utils/Concurrent_Network_Manager.php", "new_path": "-", "status": "已删除", "migration_type": "deleted", "risk_level": "最高风险", "layer": "Infrastructure", "sublayer": "Concurrency", "description": "并发网络管理器", "notes": "功能已合并到ConcurrencyManager，需要验证功能完整性", "merge_target": "infrastructure/Concurrency/ConcurrencyManager.php"}, {"id": "infra_007", "original_path": "utils/Database_Helper.php", "new_path": "infrastructure/Database/DatabaseHelper.php", "status": "跨层迁移", "migration_type": "cross_layer_migration", "risk_level": "高风险", "layer": "Infrastructure", "sublayer": "Database", "description": "数据库辅助工具", "notes": "从utils层迁移到infrastructure/Database层"}, {"id": "infra_008", "original_path": "utils/Database_Index_Manager.php", "new_path": "infrastructure/Database/IndexManager.php", "status": "重命名+重组", "migration_type": "rename_and_move", "risk_level": "中风险", "layer": "Infrastructure", "sublayer": "Database", "description": "数据库索引管理", "notes": "重命名并迁移到infrastructure/Database层"}, {"id": "infra_009", "original_path": "utils/Database_Index_Optimizer.php", "new_path": "infrastructure/Database/IndexOptimizer.php", "status": "重命名+重组", "migration_type": "rename_and_move", "risk_level": "中风险", "layer": "Infrastructure", "sublayer": "Database", "description": "数据库索引优化", "notes": "重命名并迁移到infrastructure/Database层"}, {"id": "infra_010", "original_path": "-", "new_path": "infrastructure/Database/DatabaseManager.php", "status": "新增", "migration_type": "new_file", "risk_level": "中风险", "layer": "Infrastructure", "sublayer": "Database", "description": "数据库连接管理器", "notes": "新增文件，需要确认功能来源"}, {"id": "infra_011", "original_path": "-", "new_path": "infrastructure/Database/Performance.php", "status": "新增", "migration_type": "new_file", "risk_level": "中风险", "layer": "Infrastructure", "sublayer": "Database", "description": "数据库性能监控", "notes": "新增文件，需要确认功能来源"}, {"id": "infra_012", "original_path": "-", "new_path": "infrastructure/Database/QueryBuilder.php", "status": "新增", "migration_type": "new_file", "risk_level": "中风险", "layer": "Infrastructure", "sublayer": "Database", "description": "SQL查询构建器", "notes": "新增文件，需要确认功能来源"}, {"id": "infra_013", "original_path": "core/Memory_Manager.php", "new_path": "infrastructure/Memory/MemoryManager.php", "status": "跨层迁移", "migration_type": "cross_layer_migration", "risk_level": "高风险", "layer": "Infrastructure", "sublayer": "Memory", "description": "内存使用监控和管理", "notes": "从core层迁移到infrastructure/Memory层"}, {"id": "infra_014", "original_path": "-", "new_path": "infrastructure/Memory/GarbageCollector.php", "status": "新增", "migration_type": "new_file", "risk_level": "中风险", "layer": "Infrastructure", "sublayer": "Memory", "description": "垃圾回收器", "notes": "新增文件，需要确认功能来源"}, {"id": "handlers_001", "original_path": "handlers/Import_Coordinator.php", "new_path": "handlers/ImportHandler.php", "status": "重命名迁移", "migration_type": "rename", "risk_level": "低风险", "layer": "Handlers", "description": "导入协调处理器", "notes": "重命名迁移"}, {"id": "handlers_002", "original_path": "handlers/Integrator.php", "new_path": "handlers/Integrator.php", "status": "保持不变", "migration_type": "unchanged", "risk_level": "低风险", "layer": "Handlers", "description": "系统集成处理器", "notes": "无变化"}, {"id": "handlers_003", "original_path": "handlers/Webhook.php", "new_path": "handlers/WebhookHandler.php", "status": "重命名迁移", "migration_type": "rename", "risk_level": "低风险", "layer": "Handlers", "description": "Webhook处理器", "notes": "重命名迁移"}, {"id": "handlers_004", "original_path": "api/SSE_Progress_Stream.php", "new_path": "handlers/SseHandler.php", "status": "跨层迁移", "migration_type": "cross_layer_migration", "risk_level": "高风险", "layer": "Handlers", "description": "SSE进度流处理", "notes": "从api层迁移到handlers层"}, {"id": "services_001", "original_path": "services/API.php", "new_path": "services/Api/NotionApi.php", "status": "重命名+重组", "migration_type": "rename_and_move", "risk_level": "中风险", "layer": "Services", "sublayer": "Api", "description": "Notion API交互服务", "notes": "重命名并迁移到Api子目录"}, {"id": "services_002", "original_path": "contracts/API_Interface.php", "new_path": "services/Api/ApiInterface.php", "status": "跨层迁移", "migration_type": "cross_layer_migration", "risk_level": "高风险", "layer": "Services", "sublayer": "Api", "description": "API接口定义", "notes": "从contracts层迁移到services/Api层"}, {"id": "services_003", "original_path": "services/Content_Converter.php", "new_path": "services/Content/ContentConverter.php", "status": "重命名+重组", "migration_type": "rename_and_move", "risk_level": "中风险", "layer": "Services", "sublayer": "Content", "description": "内容格式转换器", "notes": "重命名并迁移到Content子目录"}, {"id": "services_004", "original_path": "services/Database_Renderer.php", "new_path": "services/Content/DatabaseRenderer.php", "status": "重命名+重组", "migration_type": "rename_and_move", "risk_level": "中风险", "layer": "Services", "sublayer": "Content", "description": "数据库内容渲染器", "notes": "重命名并迁移到Content子目录"}, {"id": "services_005", "original_path": "services/Image_Processor.php", "new_path": "services/Content/ImageProcessor.php", "status": "重命名+重组", "migration_type": "rename_and_move", "risk_level": "中风险", "layer": "Services", "sublayer": "Content", "description": "图片处理服务", "notes": "重命名并迁移到Content子目录"}, {"id": "services_006", "original_path": "services/Metadata_Extractor.php", "new_path": "services/Content/MetadataExtractor.php", "status": "重命名+重组", "migration_type": "rename_and_move", "risk_level": "中风险", "layer": "Services", "sublayer": "Content", "description": "元数据提取器", "notes": "重命名并迁移到Content子目录"}, {"id": "services_007", "original_path": "core/Text_Processor.php", "new_path": "services/Content/TextProcessor.php", "status": "跨层迁移", "migration_type": "cross_layer_migration", "risk_level": "高风险", "layer": "Services", "sublayer": "Content", "description": "文本处理器", "notes": "从core层迁移到services/Content层"}, {"id": "services_008", "original_path": "-", "new_path": "services/Import/ImportService.php", "status": "新增", "migration_type": "new_file", "risk_level": "中风险", "layer": "Services", "sublayer": "Import", "description": "导入服务统一入口", "notes": "新增文件，需要确认功能来源"}, {"id": "services_009", "original_path": "-", "new_path": "services/Import/ImportWorkflow.php", "status": "新增", "migration_type": "new_file", "risk_level": "中风险", "layer": "Services", "sublayer": "Import", "description": "导入工作流管理", "notes": "新增文件，需要确认功能来源"}, {"id": "services_010", "original_path": "services/Sync_Manager.php", "new_path": "services/Sync/SyncManager.php", "status": "目录重组", "migration_type": "directory_move", "risk_level": "中风险", "layer": "Services", "sublayer": "Sync", "description": "同步管理器", "notes": "迁移到Sync子目录"}, {"id": "services_011", "original_path": "services/Content_Sync_Service.php", "new_path": "services/Sync/ContentSyncService.php", "status": "重命名+重组", "migration_type": "rename_and_move", "risk_level": "中风险", "layer": "Services", "sublayer": "Sync", "description": "内容同步服务", "notes": "重命名并迁移到Sync子目录"}, {"id": "services_012", "original_path": "services/Incremental_Detector.php", "new_path": "services/Sync/IncrementalDetector.php", "status": "重命名+重组", "migration_type": "rename_and_move", "risk_level": "中风险", "layer": "Services", "sublayer": "Sync", "description": "增量检测器", "notes": "重命名并迁移到Sync子目录"}, {"id": "services_013", "original_path": "-", "new_path": "services/Sync/IncrementalSyncService.php", "status": "新增", "migration_type": "new_file", "risk_level": "中风险", "layer": "Services", "sublayer": "Sync", "description": "增量同步服务", "notes": "新增文件，需要确认功能来源"}, {"id": "services_014", "original_path": "services/Task_Service.php", "new_path": "services/TaskService.php", "status": "重命名迁移", "migration_type": "rename", "risk_level": "低风险", "layer": "Services", "description": "任务服务管理", "notes": "重命名迁移"}, {"id": "utils_001", "original_path": "utils/Helper.php", "new_path": "utils/Helper.php", "status": "保持不变", "migration_type": "unchanged", "risk_level": "低风险", "layer": "Utils", "description": "通用辅助工具类", "notes": "无变化"}, {"id": "utils_002", "original_path": "utils/API_Result.php", "new_path": "utils/ApiResult.php", "status": "重命名迁移", "migration_type": "rename", "risk_level": "低风险", "layer": "Utils", "description": "API结果对象", "notes": "重命名迁移"}, {"id": "utils_003", "original_path": "utils/Async_Helper.php", "new_path": "utils/AsyncHelper.php", "status": "重命名迁移", "migration_type": "rename", "risk_level": "低风险", "layer": "Utils", "description": "异步处理辅助工具", "notes": "重命名迁移"}, {"id": "utils_004", "original_path": "utils/Config_Simplifier.php", "new_path": "utils/ConfigSimplifier.php", "status": "重命名迁移", "migration_type": "rename", "risk_level": "低风险", "layer": "Utils", "description": "配置简化器", "notes": "重命名迁移"}, {"id": "utils_005", "original_path": "utils/Network_Retry.php", "new_path": "utils/NetworkRetry.php", "status": "重命名迁移", "migration_type": "rename", "risk_level": "低风险", "layer": "Utils", "description": "网络重试机制", "notes": "重命名迁移"}, {"id": "utils_006", "original_path": "utils/Smart_API_Merger.php", "new_path": "utils/SmartApiMerger.php", "status": "重命名迁移", "migration_type": "rename", "risk_level": "低风险", "layer": "Utils", "description": "智能API合并器", "notes": "重命名迁移"}, {"id": "utils_007", "original_path": "utils/Smart_Cache.php", "new_path": "utils/SmartCache.php", "status": "重命名迁移", "migration_type": "rename", "risk_level": "低风险", "layer": "Utils", "description": "智能缓存管理器类", "notes": "重命名迁移"}, {"id": "utils_008", "original_path": "core/Validation_Rules.php", "new_path": "utils/Validator.php", "status": "跨层迁移", "migration_type": "cross_layer_migration", "risk_level": "高风险", "layer": "Utils", "description": "数据验证器", "notes": "从core层迁移到utils层"}, {"id": "admin_001", "original_path": "admin/class-notion-to-wordpress-admin.php", "new_path": "admin/Controllers/AdminController.php", "status": "架构重构", "migration_type": "architectural_refactor", "risk_level": "最高风险", "layer": "Admin", "sublayer": "Controllers", "description": "后台管理控制器", "notes": "从单一admin类重构为Controllers/Views架构，采用现代化MVC模式"}, {"id": "admin_002", "original_path": "admin/partials/notion-to-wordpress-admin-display.php", "new_path": "admin/Views/AdminDisplay.php", "status": "架构重构", "migration_type": "architectural_refactor", "risk_level": "最高风险", "layer": "Admin", "sublayer": "Views", "description": "后台管理页面视图", "notes": "从partials目录重构为Views目录，采用现代化视图模式"}], "risk_analysis": {"highest_risk_files": ["utils/Unified_Concurrency_Manager.php", "utils/Concurrent_Network_Manager.php", "admin/class-notion-to-wordpress-admin.php", "admin/partials/notion-to-wordpress-admin-display.php"], "cross_layer_migrations": ["utils/Stream_Processor.php -> core/Network/StreamProcessor.php", "core/Text_Processor.php -> services/Content/TextProcessor.php", "utils/Session_Cache.php -> infrastructure/Cache/SessionCache.php", "core/Dynamic_Concurrency_Manager.php -> infrastructure/Concurrency/ConcurrencyManager.php", "core/Task_Queue.php -> infrastructure/Concurrency/TaskQueue.php", "utils/Database_Helper.php -> infrastructure/Database/DatabaseHelper.php", "core/Memory_Manager.php -> infrastructure/Memory/MemoryManager.php", "api/SSE_Progress_Stream.php -> handlers/SseHandler.php", "contracts/API_Interface.php -> services/Api/ApiInterface.php", "core/Validation_Rules.php -> utils/Validator.php"], "new_files_requiring_verification": ["core/Performance/BatchOptimizer.php", "infrastructure/Database/DatabaseManager.php", "infrastructure/Database/Performance.php", "infrastructure/Database/QueryBuilder.php", "infrastructure/Memory/GarbageCollector.php", "services/Import/ImportService.php", "services/Import/ImportWorkflow.php", "services/Sync/IncrementalSyncService.php", "admin/Controllers/AdminController.php", "admin/Views/AdminDisplay.php"]}}