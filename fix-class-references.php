<?php
/**
 * 改进版类引用修复脚本
 * 基于 refactor-mapping.json 自动生成映射，支持更全面的引用模式
 */

function loadClassMappings($mappingFile) {
    $json = file_get_contents($mappingFile);
    $data = json_decode($json, true);
    $mappings = [];

    foreach ($data['mappings'] as $item) {
        // 只处理有原始文件和新文件的映射
        if (
            !empty($item['original_path']) && $item['original_path'] !== '-' &&
            !empty($item['new_path']) && $item['new_path'] !== '-'
        ) {
            // 解析类名
            $oldClass = pathinfo($item['original_path'], PATHINFO_FILENAME);
            $newClass = pathinfo($item['new_path'], PATHINFO_FILENAME);

            // 解析命名空间（假设NTWP为顶级命名空间）
            $oldNs = str_replace(['/', '.php'], ['\\', ''], $item['original_path']);
            $newNs = str_replace(['/', '.php'], ['\\', ''], $item['new_path']);
            $oldFqcn = '\\NTWP\\' . $oldNs;
            $newFqcn = '\\NTWP\\' . $newNs;

            $mappings[] = [
                'old_class' => $oldClass,
                'new_class' => $newClass,
                'old_fqcn'  => $oldFqcn,
                'new_fqcn'  => $newFqcn,
            ];
        }
    }
    return $mappings;
}

function buildRegexReplacements($mappings) {
    $replacements = [];

    foreach ($mappings as $map) {
        $oldClass = preg_quote($map['old_class'], '/');
        $newClass = $map['new_class'];
        $oldFqcn = preg_quote($map['old_fqcn'], '/');
        $newFqcn = $map['new_fqcn'];

        // 1. 完整命名空间引用（更精确的匹配）
        $replacements['/\b' . $oldFqcn . '\b/'] = $newFqcn;

        // 2. use语句
        $replacements['/use\s+' . substr($oldFqcn, 2) . '\s*;/'] = 'use ' . substr($newFqcn, 1) . ';';

        // 3. new 实例化
        $replacements['/new\s+' . $oldFqcn . '\s*\(/'] = 'new ' . $newFqcn . '(';
        $replacements['/new\s+' . $oldClass . '\s*\(/'] = 'new ' . $newClass . '(';

        // 4. 静态方法调用
        $replacements['/\b' . $oldClass . '::/'] = $newClass . '::';
        $replacements['/' . $oldFqcn . '::/'] = $newFqcn . '::';

        // 5. class_exists 检查
        $replacements['/class_exists\s*\(\s*[\'"]' . $oldClass . '[\'"]\s*\)/'] = "class_exists('" . $newClass . "')";
        $replacements['/class_exists\s*\(\s*[\'"]' . substr($oldFqcn, 2) . '[\'"]\s*\)/'] = "class_exists('" . substr($newFqcn, 1) . "')";

        // 6. instanceof 操作符
        $replacements['/instanceof\s+' . $oldClass . '\b/'] = 'instanceof ' . $newClass;
        $replacements['/instanceof\s+' . $oldFqcn . '\b/'] = 'instanceof ' . $newFqcn;

        // 7. 类型声明（参数、返回值、属性）
        $replacements['/(\s+)' . $oldClass . '(\s+\$)/'] = '$1' . $newClass . '$2';
        $replacements['/(:\s*)' . $oldClass . '(\s*[{;])/'] = '$1' . $newClass . '$2';

        // 8. PHPDoc 注释中的 @var, @param, @return 类型
        $replacements['/(@var\s+)' . $oldClass . '(\s)/'] = '$1' . $newClass . '$2';
        $replacements['/(@param\s+)' . $oldClass . '(\s)/'] = '$1' . $newClass . '$2';
        $replacements['/(@return\s+)' . $oldClass . '(\s)/'] = '$1' . $newClass . '$2';
    }

    return $replacements;
}

function processFile($filePath, $replacements) {
    if (!file_exists($filePath) || !is_readable($filePath)) return false;
    $content = file_get_contents($filePath);
    $originalContent = $content;
    foreach ($replacements as $pattern => $replace) {
        $content = preg_replace($pattern, $replace, $content);
    }
    if ($content !== $originalContent) {
        file_put_contents($filePath, $content);
        echo "✅ 修复: {$filePath}\n";
        return true;
    }
    return false;
}

function processDirectory($directory, $replacements) {
    $fixedCount = 0;

    if ($directory === '.') {
        // 处理根目录的PHP文件
        $rootFiles = glob('*.php');
        foreach ($rootFiles as $file) {
            if (processFile($file, $replacements)) {
                $fixedCount++;
            }
        }
        return $fixedCount;
    }

    if (!is_dir($directory)) {
        return 0;
    }

    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS)
    );

    foreach ($iterator as $file) {
        if ($file->isFile()) {
            $filePath = $file->getPathname();

            // 排除备份目录
            if (strpos($filePath, 'includes_backup') !== false) {
                continue;
            }

            $extension = $file->getExtension();
            // 处理PHP文件和Markdown文件
            if ($extension === 'php' || $extension === 'md') {
                if (processFile($filePath, $replacements)) {
                    $fixedCount++;
                }
            }
        }
    }

    return $fixedCount;
}

// === 主流程 ===
$mappingFile = __DIR__ . '/refactor-mapping.json';
$mappings = loadClassMappings($mappingFile);
$replacements = buildRegexReplacements($mappings);

$directories = [
    'includes',
    'admin',
    '.',        // 根目录文件
    'docs'      // 文档目录
];

echo "🔧 开始批量修复类引用...\n\n";
$totalFixed = 0;
foreach ($directories as $directory) {
    if (is_dir($directory)) {
        echo "📁 处理目录: {$directory}\n";
        $fixed = processDirectory($directory, $replacements);
        $totalFixed += $fixed;
        echo "   修复了 {$fixed} 个文件\n\n";
    }
}
echo "🎉 批量修复完成！总共修复了 {$totalFixed} 个文件\n";
